<template>
    <div class="up_card">
        <a-row style="margin-left: 1%;padding-top: 1%;">
            <a-col :span="2" >
                <img class="toux" :src="gameInfo.iconUrl || './component/toux.png'" alt="">
            </a-col>
            <a-col :span="8">
                <a-row style="font-size: 20px;font-weight: bold;margin-top: 1%;">{{ gameInfo.nameZh }}</a-row>
                <a-row style="font-size: 14px;font-weight: lighter;margin-top: 2%;">{{ gameInfo.developerName }}</a-row>
                <a-row style="font-size: 14px;font-weight: lighter;margin-top: 2%;">{{ gameInfo.ageRating || '12周岁+' }}</a-row>
            </a-col>
            <a-col :span="8" :offset="6">
        <div style="display: flex; align-items: center;">
          <a-select
            v-model:value="searchGameId"
            show-search
            :filter-option="false"
            :not-found-content="searching ? '加载中...' : '无数据'"
            :options="searchGameOptions"
            placeholder="搜索游戏"
            class="search_input"
            style="width: 60%; margin-top: 4%;"
            @search="onSearchGame"
            @change="onSelectGame"
            option-label-prop="label"
            @popupScroll="onPopupScroll"
          >
            <template #option="{ value, label, iconUrl }">
              <div style="display: flex; align-items: center;">
                <img :src="iconUrl" style="width: 24px; height: 24px; margin-right: 8px;" v-if="iconUrl" />
                <span>{{ label }}</span>
              </div>
            </template>
          </a-select>
          <a-button type="primary" style="margin-left: 8px; margin-top: 4%;" @click="onSearchBtn">搜索</a-button>
        </div>
      </a-col>
        </a-row>

        <div style="margin-top: 2%;margin-left: 4%;">
          <a-row>
            <a-col :span="8">
              <a-row>
                <a-col :span="24" style="font-weight: bold;font-size: 16px;">类别</a-col>
              </a-row>
              <a-row>
                <a-col :span="24" style="font-size: 16px;">{{ gameInfo.gameTag || '射击' }}</a-col>
              </a-row>
            </a-col>

            <a-col :span="8">
              <a-row>
                <a-col :span="24" style="font-weight: bold;font-size: 16px;">价格</a-col>
              </a-row>
              <a-row>
                <a-col :span="24" style="font-size: 16px;">{{ gameInfo.ageLimit || '免费' }}</a-col>
              </a-row>
            </a-col>
<!-- 
            <a-col :span="8">
              <a-row>
                <a-col :span="24" style="font-weight: bold;font-size: 16px;">热门国家/地区</a-col>
              </a-row>
              <a-row>
                <a-col :span="24" style="font-size: 16px;">{{ gameInfo.regionName || '新加坡' }}</a-col>
              </a-row>
            </a-col> -->

          </a-row>
          <a-row style="margin-top: 2%;">

            <a-col :span="8">
              <a-row>
                <a-col :span="24" style="font-weight: bold;font-size: 16px;">APP ID</a-col>
              </a-row>
              <a-row>
                <a-col :span="24" style="font-size: 16px;">{{ gameInfo.gameId }}</a-col>
              </a-row>
            </a-col>

            <a-col :span="8">
              <a-row>
                <a-col :span="24" style="font-weight: bold;font-size: 16px;">支持URL</a-col>
              </a-row>
              <a-row>
                <a-col :span="24" style="font-size: 16px;">
                  <a
                    v-if="gameInfo.developerUrl"
                    :href="gameInfo.developerUrl"
                    target="_blank"
                    style="color: #29ade6; word-break: break-all;"
                  >
                    {{ gameInfo.developerUrl.length > 32 ? gameInfo.developerUrl.slice(0, 32) + '...' : gameInfo.developerUrl }}
                  </a>
                  <template v-else>
                    上海游戏银河有限公司
                  </template>
                </a-col>
              </a-row>
            </a-col>

            <a-col :span="8">
              <a-row>
                <a-col :span="24" style="font-weight: bold;font-size: 16px;">开发者</a-col>
              </a-row>
              <a-row>
                <a-col :span="24" style="font-size: 16px;">{{gameInfo.developerId}}</a-col>
              </a-row>
            </a-col>

          </a-row>
        </div>
    </div>

    <div class="middle_card">
        <a-row >
            <a-col :span="8" >
                <div class="middle_small_card">
                    <a-row>
                        <a-col :span="24" style="font-weight: bold;margin-top: 3.5%;margin-left:5%;font-size: 16px;">下载</a-col>
                    </a-row>
                    <a-row>
                        <a-col :span="24" style="font-weight: lighter;margin-left: 5%;margin-top: 1%;">全球·上月</a-col>
                    </a-row>
                    <div style="font-size: 52px;color: #ffa66e;text-align: center;">
                        {{ gameInfo.downloadTimes || '0' }}
                    </div>
                </div>
            </a-col>
            <a-col :span="8">
                <div class="middle_small_card" style="margin-left: 5%;">
                    <a-row>
                        <a-col :span="24" style="font-weight: bold;margin-top: 3.5%;margin-left:5%;font-size: 16px;">收入(美元)</a-col>
                    </a-row>
                    <a-row>
                        <a-col :span="24" style="font-weight: lighter;margin-left: 5%;margin-top: 1%;">全球·上月</a-col>
                    </a-row>
                    <div style="font-size: 52px;color: #ffa66e;text-align: center;">
                        {{ gameInfo.income || '0' }}
                    </div>
                </div>
            </a-col>
            <a-col :span="8">
                  <div class="middle_small_card" style="margin-left: 9%;">
                    <a-row>
                        <a-col :span="24" style="font-weight: bold;margin-top: 3.5%;margin-left:5%;font-size: 16px;">类别排名</a-col>
                    </a-row>
                    <a-row>
                        <a-col :span="24" style="font-weight: lighter;margin-left: 5%;margin-top: 1%;">全球·上月</a-col>
                    </a-row>
                    <div style="font-size: 52px;color: #ffa66e;text-align: center;">
                        {{ gameInfo.ranking || '0' }}
                    </div>
                </div>
            </a-col>
        </a-row>
    </div>

    <div class="bottom_card">
        <div class="introduction" v-show="chear_jie">
            <div class="introduction_title">
                <a-row style="padding-top: 10px;">
                    <a-col :span="5" style="font-weight: bold;font-size: 16px; color: #29ade6;" >
                      <div @click="go_jie">介绍</div>
                      <div style="width: 36px;height: 2px;background-color: #29ade6;"></div>
                    </a-col>
                    <a-col :span="8" style="font-weight: bold;font-size: 16px;"><div  @click="go_chan">产品排名</div></a-col>
                    <a-col :span="9" style="font-weight: bold;font-size: 16px;"> <div @click="go_pin">评分与评论</div></a-col>
                </a-row>
            </div>

            <div class="Information">
                <a-row>
                    <a-col :span="3">
                        <a-row>
                            <a-col :span="24" >发行日期</a-col>
                        </a-row>
                        <a-row>
                            <a-col :span="24" style="font-weight: lighter;">{{ gameInfo.releaseDate || '2024/4/17' }}</a-col>
                        </a-row>
                    </a-col>
                    <a-col :span="3">
                        <a-row>
                            <a-col :span="24" >当前版本</a-col>
                        </a-row>
                        <a-row>
                            <a-col :span="24" style="font-weight: lighter;">{{ gameInfo.version || '10.2.6' }}</a-col>
                        </a-row>
                    </a-col>
                    <a-col :span="3">
                        <a-row>
                            <a-col :span="24" >发行商国家</a-col>
                        </a-row>
                        <a-row>
                            <a-col :span="24" style="font-weight: lighter;">{{ gameInfo.region || '马来西亚' }}</a-col>
                        </a-row>
                    </a-col>
                    <a-col :span="4">
                        <a-row>
                            <a-col :span="24" >上次更新时间</a-col>
                        </a-row>
                        <a-row>
                            <a-col :span="24" style="font-weight: lighter;">{{ gameInfo.lastUpdated || '2024/9/19' }}</a-col>
                        </a-row>
                    </a-col>
                    <a-col :span="5">
                        <a-row>
                            <a-col :span="24" >最低操作系统版本</a-col>
                        </a-row>
                        <a-row>
                            <a-col :span="24" style="font-weight: lighter;">{{ gameInfo.minOsVersion || '11.0' }}</a-col>
                        </a-row>
                    </a-col>
                    <a-col :span="3">
                        <a-row>
                            <a-col :span="24" >文件大小</a-col>
                        </a-row>
                        <a-row>
                            <a-col :span="24" style="font-weight: lighter;">{{ gameInfo.appSize || '1.1G' }}</a-col>
                        </a-row>
                    </a-col>
                    <a-col :span="3">
                        <a-row>
                            <a-col :span="24" >语言</a-col>
                        </a-row>
                        <a-row>
                            <a-col :span="24" style="font-weight: lighter;">{{ gameInfo.language || '简体中文' }}</a-col>
                        </a-row>
                    </a-col>
                </a-row>

                <div style=" font-weight: bold;font-size: 16px;margin-top: 2%;">
                  游戏简介
                </div>
                <div style="width: 4.2%;height: 2px;background-color: #29ade6; border-radius: 1px 1px 1px 1px;"></div>
                <div style="margin-left: 1%;margin-top: 1%;">
                  {{ gameInfo.description || '《王者荣耀》是游戏银河第一5V5团队公平竞技手游，国民MOBA手游大作！5V5王者峡谷、公平对战，还原MOBA经典体验;契约之战、五军对决、边境突围、王者模拟战等，带来花式作战乐趣！10秒实时跨区匹配，与好友开黑上分，向最强王者进击！多款英雄任凭选择，一血、五杀、超神，实力碾压，收割全场！敌军即将到达战场，王者召唤师快来集结好友，准备团战，就在《星际探险者》！' }}
                </div>
                <div style=" font-weight: bold;font-size: 16px;margin-top: 2%;">
                  游戏截图
                </div>
                <div style="width: 4.2%;height: 2px;background-color: #29ade6; border-radius: 1px 1px 1px 1px;"></div>
                <!-- <div style="margin-top:2%">
                    <img :width="200" :src="gameInfo.screenshot || './component/1.png'" />
                   
                </div> -->
                <div style="margin-top:2%">
                  <template v-if="screenshotList.length">
                    <img
                      v-for="(url, idx) in screenshotList"
                      :key="idx"
                      :width="200"
                      :src="url.trim()"
                      style="margin-right: 12px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.08);"
                    />
                  </template>
                  <template v-else>
                    <!-- <img :width="200" src="./component/1.png" /> -->
                  </template>
                </div>
            </div>
        </div>

        <div class="ranking" v-show="chear_chan">
          <div class="introduction_title">
                <a-row style="padding-top: 10px;">
                    <a-col :span="5" style="font-weight: bold;font-size: 16px;" @click="go_jie">介绍</a-col>
                    <a-col :span="8" style="font-weight: bold;font-size: 16px;color: #29ade6;" @click="go_chan">产品排名
                      <div style="width: 66px;height: 2px;background-color: #29ade6;" ></div>
                    </a-col>
                    <a-col :span="9" style="font-weight: bold;font-size: 16px;" @click="go_pin">评分与评论</a-col>
                </a-row>
          </div>
          <div id="ranking_echar" style="width: 1200px;height:  500%;margin-top: 4%;margin-left: 14%;"></div>
        </div>

        <div class="comments"  v-show="chear_pin">
          <div class="introduction_title">
                <a-row style="padding-top: 10px;">
                    <a-col :span="5" style="font-weight: bold;font-size: 16px;" @click="go_jie">介绍</a-col>
                    <a-col :span="8" style="font-weight: bold;font-size: 16px;" @click="go_chan">产品排名</a-col>
                    <a-col :span="9" style="font-weight: bold;font-size: 16px;color: #29ade6;" @click="go_pin">评分与评论
                      <div style="width: 82px;height: 2px;background-color: #29ade6;"></div>
                    </a-col>
                </a-row>
          </div>
          <!-- 综合评分部分，替换为动态绑定 -->
<div style="font-weight: bold;font-size: 16px;margin-top: 0.3%;margin-left: 1%;">综合评分</div>
<div style="background-color: #29ade6;width: 65px;height: 3px;margin-left: 15px;"></div>
<a-row style="margin-left: 27%;">
  <a-col :span="3">
    <div style="font-size: 72px;">{{ commentSummary.rating }}</div>
    <div style="margin-top: -10px;margin-left: -12%;">
      <a-rate :value="commentSummary.rating" count="5" allow-half disabled />
    </div>
    <div style="width: 120%;margin-top: 12%;margin-left: -12%">
      {{ (commentSummary.reviewCount )}}人参与评分
      <span v-if="commentSummary.goodRate">好评率{{ (commentSummary.goodRate * 100).toFixed(1) }}%</span>
    </div>
  </a-col>
  <a-col :span="12">
    <a-row v-for="star in [5,4,3,2,1]" :key="star">
      <a-col :span="7">
        <a-rate :value="star" :count="star" disabled style="position: relative; left: calc((5 - star) * 28px);" />
      </a-col>
      <a-col :span="12" style="margin-top: 6px;">
        <a-row style="width: 180%;">
          <a-col :span="19">
            <a-progress
              :percent="getStarPercent(star)"
              :show-info="false"
            />
          </a-col>
          <a-col :span="4" :offset="1">
            {{ commentSummary.starCounts[5 - star] || 0 }}
          </a-col>
        </a-row>
      </a-col>
    </a-row>
  </a-col>
</a-row>
          <div style="font-weight: bold;font-size: 16px;margin-top: 2%;margin-left: 1%;">用户评论</div>
          <div style="background-color: #29ade6;width: 65px;height: 3px;margin-left: 15px;"></div>

          <!-- 调试信息 -->
          <div style="background: #f0f0f0; padding: 10px; margin: 10px; border-radius: 5px;">
            <p><strong>调试信息：</strong></p>
            <p>评论数量: {{ comments ? comments.length : 0 }}</p>
            <p>评论数据: {{ JSON.stringify(comments, null, 2) }}</p>
          </div>

          <template v-if="comments && comments.length > 0">
            <div v-for="(item,index) in comments" style="border-left: 3px solid #e4e6e7; margin-top: 2%; margin-left: 2%;">
              <div style="margin-left: 12px;font-size: 16px;font-weight: bold;">{{item.name}}</div>
              <a-rate style="margin-left: 12px;" class="comment_star" :value="Number(item.star)" count="5" disabled />
              <div style="margin-left: 12px;  font-weight: lighter;font-size: 12px;">{{ item.platform }}  {{ item.time }}</div>
              <div style="margin-left: 12px; font-weight: lighter;font-size: 12px;">{{ item.ip }}</div>
              <div style="margin-left: 12px;margin-top: 8px;">{{ item.comment }}</div>
            </div>
          </template>
          <template v-else>
            <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 40px 0;">
              <div style="font-size: 48px; color: #d9d9d9; margin-bottom: 16px;">💬</div>
              <div style="font-size: 16px; color: #666;">暂无用户评论</div>
            </div>
          </template>

        </div>

    </div>
  </template>
  
  <script lang="ts" name="basic-table-demo" setup>
  import { ref, onMounted } from 'vue';
  import { SearchOutlined } from '@ant-design/icons-vue';
  import * as echarts from 'echarts';
  import { defHttp } from '/@/utils/http/axios';
  import { findGamesByPrefixApi } from '@/api/public-opinion-monitoring';
  import { useRoute,useRouter } from 'vue-router';
  import dayjs from 'dayjs';
  import { 
    queryByIdApi,
    getCommentApi,
    getCommentCountApi,

   } from '/@/api/store-information/market-dynamics/popular-ranking/application-details'; 


    // import{ AImage } from '@ant-design'
    // import imgg from './component/toux.png'
    const route = useRoute();
    const router = useRouter();
    // 切换介绍，产品排名，评论
    const chear_jie = ref(true);
    const chear_chan = ref(false);
    const chear_pin = ref(false);
    // 新增：游戏详情数据 
    const gameInfo = ref<any>({});
    const screenshotList = ref<string[]>([]);
    // 搜索相关
    const searchGameId = ref('');
    const searchGameOptions = ref<any[]>([]);
    const searching = ref(false);
    const searchGamePage = ref(1);
    const searchGamePageSize = 10;
    const searchGameTotal = ref(0);
    const searchGameKeyword = ref('');
    // 搜索框分页远程搜索
    const onSearchGame = async (value: string) => {
      searchGameKeyword.value = value;
      await fetchGameOptions();
    };
    const onPopupScroll = async (e: Event) => {
      // 前缀树API不支持分页，所以不需要滚动加载更多
      // 保留函数以避免模板报错，但不执行任何操作
    };
    const fetchGameOptions = async (append = false) => {
      searching.value = true;
      try {
        const res = await findGamesByPrefixApi({
          prefix: searchGameKeyword.value || '',
        });
        const records = res?.records || [];
        searchGameTotal.value = records.length;
        const options = records.map((item: any) => ({
          label: item.nameZh || item.gameName || item.name,
          value: item.appId || item.gameId || item.id,
          iconUrl: item.iconUrl || item.icon || '',
        }));
        // 前缀树API返回所有匹配结果，直接替换
        searchGameOptions.value = options;
      } finally {
        searching.value = false;
      }
    };

    // 选中某个游戏
    const onSelectGame = (gameId: string) => {
      searchGameId.value = gameId;
    };

    // 点击搜索按钮，跳转到详情
    const onSearchBtn = () => {
      if (!searchGameId.value) return;
      router.push({
      name: 'applicationDetails',
      query: {
        appId: searchGameId.value,
      },
    });
    };
    // 新增：综合评分数据
    const commentSummary = ref({
      rating: 0,
      reviewCount: 0,
      goodRate: 0,
      starCounts: [0, 0, 0, 0, 0], // 5星到1星
    });

    function go_jie() {
      chear_jie.value = true;
      chear_chan.value = false;
      chear_pin.value = false;
    }

    function go_chan() {
      chear_jie.value = false;
      chear_chan.value = true;
      chear_pin.value = false;
    }

    function go_pin() {
      chear_jie.value = false;
      chear_chan.value = false;
      chear_pin.value = true;
    }

    // 评论数据
    const comments=ref([
      {
        name:"llili",
        comment:"《幻影街编年史》以其独特的艺术风格和深刻的故事线吸引了我。游戏中的每一个角色都有自己的背景故事，让玩家在探索这个幻想城市的同时，也能深入了解每个角色的内心世界。解谜元素与故事情节完美结合，使得每一次冒险都充满了新鲜感。",
        time:"2025/2/4",
        platform:"App Store",
        ip:"北京",
        star:"4"
      },
      {
        name:"llili",
        comment:"这是一款非常适合策略爱好者的游戏。它不仅提供了广阔的宇宙空间供玩家探索，还通过复杂的外交系统和战斗机制增加了游戏的深度。尽管初期上手难度较大，但是一旦掌握了基本规则，《星际征途：奥瑞恩协议》就能提供数小时的沉浸式游戏体验。",
        time:"2025/2/4",
        platform:"App Store",
        ip:"北京",
        star:"4"
      },
      {
        name:"llili",
        comment:"末日工坊》是一款出色的生存建造类游戏，它挑战了玩家在资源稀缺的世界中求生的能力。虽然游戏的图形表现并不是最顶尖的，但是其创新的建造和资源管理系统让人印象深刻。游戏中的天气变化和动态事件为玩家带来了额外的挑战和乐趣。",
        time:"2025/2/4",
        platform:"App Store",
        ip:"北京",
        star:"4"
      }
    ]);

    // 搜索数据
    const Search_content=ref();
    let myChart = ref()
    let option = ref({})

    onMounted(async () => {
  await fetchData();
  await fetchCommentSummary();
    await fetchComments(); 
     await fetchGameOptions();
  init();
})
const fetchData = async () => {
  try {
    const id = route.query.appId || route.query.id || "000000000000100";
    // const response = await defHttp.get({ url: '/appInfo/queryById', params: { appId: id } });
    const response = await queryByIdApi({ appId: id });
    console.log('应用详情：',response)
    gameInfo.value = response;
    // 处理 screenshotUrls 字段
    let urls = response.screenshotUrls;
    if (urls) {
      if (Array.isArray(urls)) {
        screenshotList.value = urls;
      } else if (typeof urls === 'string') {
        try {
          // 先去除首尾引号
          let str = urls.trim();
          if (str.startsWith('"') && str.endsWith('"')) {
            str = str.slice(1, -1);
          }
          // 还原转义
          str = str.replace(/\\"/g, '"');
          // 再 JSON.parse 一次
          const arr = JSON.parse(str);
          screenshotList.value = Array.isArray(arr)
            ? arr.map(u => u.trim())
            : [];
        } catch {
          screenshotList.value = [];
        }
      } else {
        screenshotList.value = [];
      }
    } else {
      screenshotList.value = [];
    }
    // 新增：处理排名历史
    //rankingHistory.value = Array.isArray(response.rankingHistory) ? response.rankingHistory : [];
  } catch (error) {
    console.error('数据获取失败:', error);
    screenshotList.value = [];
    //rankingHistory.value = [];
  }
};


const fetchComments = async () => {
  try {
    const appId = route.query.appId || route.query.id || "6444826882";
    // 尝试从 getCommentCountApi 获取评论数据
    const res = await getCommentCountApi({ appId });
    console.log('评论数据（从getCommentCountApi）：',res)

    // 检查返回数据中是否包含评论列表
    let commentList: any[] = [];
    if (Array.isArray(res)) {
      commentList = res;
    } else if (res && res.comments) {
      commentList = res.comments;
    } else if (res && res.records) {
      commentList = res.records;
    } else if (res && res.data) {
      commentList = res.data;
    }

    console.log('提取的评论列表：', commentList);
    console.log('第一条评论数据结构：', commentList[0]);
    comments.value = Array.isArray(commentList) ? commentList : [];
    console.log('设置后的comments.value：', comments.value);
  } catch (e) {
    console.error('获取评论失败:', e);
    comments.value = [];
  }
};
// 新增：获取综合评分数据
const fetchCommentSummary = async () => {
  try {
    const appId = route.query.appId || route.query.id || "000000000000100";
    // const res = await defHttp.get({ url: '/shop/getCommentCount', params: { appId:appId}});
    const res = await getCommentCountApi({ appId });
    // 假设返回格式为 { rating: 4.5, reviewCount: 12345, goodRate: 0.98, starCounts: [5000, 2000, 1000, 500, 200] }
// 构造 5星到1星的数量数组
    const starCounts: number[] = [0, 0, 0, 0, 0];
    let totalScore = 0;
    let totalCount = 0;
    res.forEach((item: { point: number; count: number }) => {
      if (item.point >= 1 && item.point <= 5) {
        starCounts[5 - item.point] = item.count;
        totalScore += item.point * item.count;
        totalCount += item.count;
      }
    });
    // 计算平均分
    const rating = totalCount ? (totalScore / totalCount) : 0;
    // 计算好评率（4星和5星占比）
    const goodCount = (starCounts[0] || 0) + (starCounts[1] || 0);
    const goodRate = totalCount ? (goodCount / totalCount) : 0;
    commentSummary.value.rating = Number(rating.toFixed(1));
    commentSummary.value.reviewCount = totalCount;
    commentSummary.value.goodRate = goodRate;
    commentSummary.value.starCounts = starCounts;
  } catch (e) {
  }
};
function getStarPercent(star: number) {
  const total = commentSummary.value.starCounts.reduce((a, b) => a + b, 0) || 1;
  return Math.round((commentSummary.value.starCounts[5 - star] / total) * 100);
}
    // 初始化波浪图
const init = () => {
  // 只展示当前详情的一个点
  const rawDate = gameInfo.value.lastUpdated || '';
const formattedDate = rawDate ? dayjs(new Date(rawDate)).format('YYYYMMDD') : '';
  const xData = [formattedDate || ''];
  const yData = [gameInfo.value.ranking || 0];
  const descriptions = [gameInfo.value.lastDescription || ''];

  myChart.value = echarts.init(document.getElementById('ranking_echar'));
  option.value = {
    title: { text: '' },
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        // 只有一个点
        const idx = params[0].dataIndex;
        return `
          <div>
            <div>日期: ${xData[idx]}</div>
            <div>排名: ${yData[idx]}</div>
            <div>描述: ${descriptions[idx] || ''}</div>
          </div>
        `;
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData,
      name: '日期'
    },
    yAxis: {
      type: 'value',
      name: '排名'
    },
    series: [
      {
        name: '排名',
        type: 'line',
        stack: 'Total',
        data: yData,
        areaStyle: {}, // 波浪填充
        symbol: 'circle',
        smooth: true
      }
    ]
  };
  myChart.value.setOption(option.value);
};
    
    
  </script>

  <style scoped>
  .Information{
    width: 95%;
    margin-left: 3%;
    margin-top: 1%;
    height: 100px;
  }
  .introduction_title{
    width: 230px;
    height: 50px;
    margin-left: 1%;
    
  }
  .bottom_card{
    width: 98.8%;
    height: 60%;
    margin-left: 12px;
    margin-top: 2%;
    background-color: #ffffff;
    border-radius: 8px 8px 8px 8px;
  }
  .middle_card{
    margin-top: 2%;
    margin-left: 12px;
  }
  .middle_small_card{
    width: 90%;
    height:8.2vw;
    background-color: #ffffff;
    border-radius: 8px 8px 8px 8px;
  }
    .up_card{
        width: 98.8%;
        margin-left: 12px;
        margin-top: 0.5%;
        height: 17vw;
        background-color: #ffffff;
        border-radius: 8px 8px 8px 8px;
    }

    .toux{
      width:80px;
      border-radius: 8px 8px 8px 8px;
      margin-left: 10%;
      margin-top: 8%;
    }
  /* 搜索框样式 */
.search_input {
  width: 60%;
  margin-top: 4%;
}
</style>