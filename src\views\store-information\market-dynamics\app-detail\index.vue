<template>
  <div class="app-detail-container">
    <div class="app-header-card">
        <div class="header-content">
            <div class="app-info-section">
                <div class="app-icon-wrapper">
                    <img class="app-icon" :src="gameInfo.iconUrl || './component/toux.png'" alt="">
                </div>
                <div class="app-details">
                    <h1 class="app-title">{{ gameInfo.nameZh }}</h1>
                    <p class="app-developer">{{ gameInfo.developerName }}</p>
                    <div class="app-rating-badge">{{ gameInfo.ageRating || '12周岁+' }}</div>
                </div>
            </div>
            <div class="search-section">
                <div class="search-wrapper">
                    <a-select
                        v-model:value="searchGameId"
                        show-search
                        :filter-option="false"
                        :not-found-content="searching ? '加载中...' : '无数据'"
                        :options="searchGameOptions"
                        placeholder="搜索其他游戏..."
                        class="search-select"
                        @search="onSearchGame"
                        @change="onSelectGame"
                        option-label-prop="label"
                        @popupScroll="onPopupScroll"
                    >
                        <template #option="{ value, label, iconUrl }">
                            <div class="search-option">
                                <img :src="iconUrl" class="option-icon" v-if="iconUrl" />
                                <span>{{ label }}</span>
                            </div>
                        </template>
                    </a-select>
                    <a-button type="primary" class="search-btn" @click="onSearchBtn">
                        <SearchOutlined />
                        搜索
                    </a-button>
                </div>
            </div>
        </div>

        <div class="app-info-grid">
            <div class="info-item">
                <div class="info-label">类别</div>
                <div class="info-value">{{ gameInfo.gameTag || '射击' }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">价格</div>
                <div class="info-value price">{{ gameInfo.ageLimit || '免费' }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">APP ID</div>
                <div class="info-value app-id">{{ gameInfo.gameId }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">支持URL</div>
                <div class="info-value">
                    <a
                        v-if="gameInfo.developerUrl"
                        :href="gameInfo.developerUrl"
                        target="_blank"
                        class="support-url"
                    >
                        {{ gameInfo.developerUrl.length > 32 ? gameInfo.developerUrl.slice(0, 32) + '...' : gameInfo.developerUrl }}
                    </a>
                    <span v-else class="default-company">上海游戏银河有限公司</span>
                </div>
            </div>
            <div class="info-item">
                <div class="info-label">开发者</div>
                <div class="info-value">{{ gameInfo.developerId }}</div>
            </div>
        </div>
    </div>

    <div class="stats-section">
        <div class="stats-grid">
            <div class="stat-card download-card">
                <div class="stat-icon">📱</div>
                <div class="stat-content">
                    <div class="stat-title">下载</div>
                    <div class="stat-subtitle">全球·上月</div>
                    <div class="stat-value download-value">
                        {{ formatNumber(gameInfo.downloadTimes || '0') }}
                    </div>
                </div>
            </div>
            <div class="stat-card revenue-card">
                <div class="stat-icon">💰</div>
                <div class="stat-content">
                    <div class="stat-title">收入(美元)</div>
                    <div class="stat-subtitle">全球·上月</div>
                    <div class="stat-value revenue-value">
                        {{ formatNumber(gameInfo.income || '0') }}
                    </div>
                </div>
            </div>
            <div class="stat-card ranking-card">
                <div class="stat-icon">🏆</div>
                <div class="stat-content">
                    <div class="stat-title">类别排名</div>
                    <div class="stat-subtitle">全球·上月</div>
                    <div class="stat-value ranking-value">
                        {{ gameInfo.ranking || '0' }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="bottom_card">
        <div class="introduction" v-show="chear_jie">
            <div class="introduction_title">
                <a-row style="padding-top: 10px;">
                    <a-col :span="5" style="font-weight: bold;font-size: 16px; color: #29ade6;" >
                      <div @click="go_jie">介绍</div>
                      <div style="width: 36px;height: 2px;background-color: #29ade6;"></div>
                    </a-col>
                    <a-col :span="8" style="font-weight: bold;font-size: 16px;"><div  @click="go_chan">产品排名</div></a-col>
                    <a-col :span="9" style="font-weight: bold;font-size: 16px;"> <div @click="go_pin">评分与评论</div></a-col>
                </a-row>
            </div>

            <div class="Information">
                <a-row>
                    <a-col :span="3">
                        <a-row>
                            <a-col :span="24" >发行日期</a-col>
                        </a-row>
                        <a-row>
                            <a-col :span="24" style="font-weight: lighter;">{{ gameInfo.releaseDate || '2024/4/17' }}</a-col>
                        </a-row>
                    </a-col>
                    <a-col :span="3">
                        <a-row>
                            <a-col :span="24" >当前版本</a-col>
                        </a-row>
                        <a-row>
                            <a-col :span="24" style="font-weight: lighter;">{{ gameInfo.version || '10.2.6' }}</a-col>
                        </a-row>
                    </a-col>
                    <a-col :span="3">
                        <a-row>
                            <a-col :span="24" >发行商国家</a-col>
                        </a-row>
                        <a-row>
                            <a-col :span="24" style="font-weight: lighter;">{{ gameInfo.region || '马来西亚' }}</a-col>
                        </a-row>
                    </a-col>
                    <a-col :span="4">
                        <a-row>
                            <a-col :span="24" >上次更新时间</a-col>
                        </a-row>
                        <a-row>
                            <a-col :span="24" style="font-weight: lighter;">{{ gameInfo.lastUpdated || '2024/9/19' }}</a-col>
                        </a-row>
                    </a-col>
                    <a-col :span="5">
                        <a-row>
                            <a-col :span="24" >最低操作系统版本</a-col>
                        </a-row>
                        <a-row>
                            <a-col :span="24" style="font-weight: lighter;">{{ gameInfo.minOsVersion || '11.0' }}</a-col>
                        </a-row>
                    </a-col>
                    <a-col :span="3">
                        <a-row>
                            <a-col :span="24" >文件大小</a-col>
                        </a-row>
                        <a-row>
                            <a-col :span="24" style="font-weight: lighter;">{{ gameInfo.appSize || '1.1G' }}</a-col>
                        </a-row>
                    </a-col>
                    <a-col :span="3">
                        <a-row>
                            <a-col :span="24" >语言</a-col>
                        </a-row>
                        <a-row>
                            <a-col :span="24" style="font-weight: lighter;">{{ gameInfo.language || '简体中文' }}</a-col>
                        </a-row>
                    </a-col>
                </a-row>

                <div style=" font-weight: bold;font-size: 16px;margin-top: 2%;">
                  游戏简介
                </div>
                <div style="width: 4.2%;height: 2px;background-color: #29ade6; border-radius: 1px 1px 1px 1px;"></div>
                <div style="margin-left: 1%;margin-top: 1%;">
                  {{ gameInfo.description || '《王者荣耀》是游戏银河第一5V5团队公平竞技手游，国民MOBA手游大作！5V5王者峡谷、公平对战，还原MOBA经典体验;契约之战、五军对决、边境突围、王者模拟战等，带来花式作战乐趣！10秒实时跨区匹配，与好友开黑上分，向最强王者进击！多款英雄任凭选择，一血、五杀、超神，实力碾压，收割全场！敌军即将到达战场，王者召唤师快来集结好友，准备团战，就在《星际探险者》！' }}
                </div>
                <div style=" font-weight: bold;font-size: 16px;margin-top: 2%;">
                  游戏截图
                </div>
                <div style="width: 4.2%;height: 2px;background-color: #29ade6; border-radius: 1px 1px 1px 1px;"></div>
                <!-- <div style="margin-top:2%">
                    <img :width="200" :src="gameInfo.screenshot || './component/1.png'" />
                   
                </div> -->
                <div style="margin-top:2%">
                  <template v-if="screenshotList.length">
                    <img
                      v-for="(url, idx) in screenshotList"
                      :key="idx"
                      :width="200"
                      :src="url.trim()"
                      style="margin-right: 12px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.08);"
                    />
                  </template>
                  <template v-else>
                    <!-- <img :width="200" src="./component/1.png" /> -->
                  </template>
                </div>
            </div>
        </div>

        <div class="ranking" v-show="chear_chan">
          <div class="introduction_title">
                <a-row style="padding-top: 10px;">
                    <a-col :span="5" style="font-weight: bold;font-size: 16px;" @click="go_jie">介绍</a-col>
                    <a-col :span="8" style="font-weight: bold;font-size: 16px;color: #29ade6;" @click="go_chan">产品排名
                      <div style="width: 66px;height: 2px;background-color: #29ade6;" ></div>
                    </a-col>
                    <a-col :span="9" style="font-weight: bold;font-size: 16px;" @click="go_pin">评分与评论</a-col>
                </a-row>
          </div>
          <div id="ranking_echar" class="chart-container"></div>
        </div>

        <div class="comments" v-show="chear_pin">
          <div class="introduction_title">
                <a-row style="padding-top: 10px;">
                    <a-col :span="5" style="font-weight: bold;font-size: 16px;" @click="go_jie">介绍</a-col>
                    <a-col :span="8" style="font-weight: bold;font-size: 16px;" @click="go_chan">产品排名</a-col>
                    <a-col :span="9" style="font-weight: bold;font-size: 16px;color: #29ade6;" @click="go_pin">评分与评论
                      <div style="width: 82px;height: 2px;background-color: #29ade6;"></div>
                    </a-col>
                </a-row>
          </div>
          <!-- 综合评分部分，替换为动态绑定 -->
<div class="rating-section-title">综合评分</div>
<div class="rating-section-divider"></div>
<div class="rating-summary-container">
  <div class="rating-overview">
    <div class="rating-score">{{ commentSummary.rating }}</div>
    <div class="rating-stars">
      <a-rate :value="commentSummary.rating" count="5" allow-half disabled />
    </div>
    <div class="rating-info">
      {{ commentSummary.reviewCount }}人参与评分
      <div v-if="commentSummary.goodRate" class="good-rate">
        好评率{{ (commentSummary.goodRate * 100).toFixed(1) }}%
      </div>
    </div>
  </div>
  <div class="rating-breakdown">
    <div v-for="star in [5,4,3,2,1]" :key="star" class="star-row">
      <div class="star-label">
        <a-rate :value="star" :count="star" disabled />
      </div>
      <div class="star-progress">
        <a-progress
          :percent="getStarPercent(star)"
          :show-info="false"
        />
      </div>
      <div class="star-count">
        {{ commentSummary.starCounts[5 - star] || 0 }}
      </div>
    </div>
  </div>
</div>
          <div class="comments-header">
            <div class="rating-section-title" style="margin-top: 2%;">用户评论</div>
            <div class="page-indicator" v-if="commentPagination.total > 0">
              第 {{ commentPagination.current }} 页评论
            </div>
          </div>
          <div class="rating-section-divider"></div>



          <div class="comments-list-container">
            <div v-if="commentLoading" class="comments-loading">
              <a-spin size="large" />
              <div class="loading-text">正在加载评论...</div>
            </div>
            <template v-else-if="comments && comments.length > 0">
              <transition-group name="comment-fade" tag="div">
                <div v-for="(item,index) in comments" :key="`${commentPagination.current}-${index}`" class="comment-item">
                  <div class="comment-header">
                    <div class="comment-user">{{item.name}}</div>
                    <a-rate class="comment-star" :value="Number(item.star)" count="5" disabled />
                  </div>
                  <div class="comment-meta">
                    <span class="comment-platform">{{ item.platform }}</span>
                    <span class="comment-time">{{ item.time }}</span>
                    <span class="comment-location">{{ item.ip }}</span>
                  </div>
                  <div class="comment-content">{{ item.comment }}</div>
                </div>
              </transition-group>
            </template>
            <template v-else>
              <div class="no-comments">
                <div class="no-comments-icon">💬</div>
                <div class="no-comments-text">暂无用户评论</div>
              </div>
            </template>
          </div>
          <!-- 分页组件 -->
          <div class="comments-pagination" v-if="commentPagination.total > 0">
            <div class="pagination-left">
              <div class="pagination-size">
                <span class="pagination-label">每页显示:</span>
                <select v-model="commentPagination.pageSize" class="page-size-select" @change="onPageSizeChange">
                  <option v-for="size in pageSizeOptions" :key="size" :value="size">{{ size }}</option>
                </select>
              </div>
              <div class="total-count">
                共 <span class="total-pages">{{ commentPagination.total }}</span> 条评论
              </div>
            </div>
            <div class="pagination-center">
              <button
                class="pagination-btn prev-btn"
                @click="onCommentPageChange(commentPagination.current - 1)"
                :disabled="commentPagination.current <= 1"
              >
                <span>← 上一页</span>
              </button>
              <div class="pagination-info">
                <span class="current-page">{{ commentPagination.current }}</span>
                <span class="page-separator">/</span>
                <span class="total-page">{{ Math.ceil(commentPagination.total / commentPagination.pageSize) }}</span>
              </div>
              <button
                class="pagination-btn next-btn"
                @click="onCommentPageChange(commentPagination.current + 1)"
                :disabled="commentPagination.current >= Math.ceil(commentPagination.total / commentPagination.pageSize)"
              >
                <span>下一页 →</span>
              </button>
            </div>
          </div>

        </div>

    </div>
  </div>
</template>
  
  <script lang="ts" name="basic-table-demo" setup>
  import { ref, onMounted } from 'vue';
  import { SearchOutlined } from '@ant-design/icons-vue';
  import * as echarts from 'echarts';
  import { defHttp } from '/@/utils/http/axios';
  import { findGamesByPrefixApi } from '@/api/public-opinion-monitoring';
  import { useRoute,useRouter } from 'vue-router';
  import dayjs from 'dayjs';
  import { 
    queryByIdApi,
    getCommentApi,
    getCommentCountApi,

   } from '/@/api/store-information/market-dynamics/popular-ranking/application-details'; 


    // import{ AImage } from '@ant-design'
    // import imgg from './component/toux.png'
    const route = useRoute();
    const router = useRouter();
    // 切换介绍，产品排名，评论
    const chear_jie = ref(true);
    const chear_chan = ref(false);
    const chear_pin = ref(false);
    // 新增：游戏详情数据 
    const gameInfo = ref<any>({});
    const screenshotList = ref<string[]>([]);
    // 搜索相关
    const searchGameId = ref('');
    const searchGameOptions = ref<any[]>([]);
    const searching = ref(false);
    const searchGamePage = ref(1);
    const searchGamePageSize = 10;
    const searchGameTotal = ref(0);
    const searchGameKeyword = ref('');
    // 搜索框分页远程搜索
    const onSearchGame = async (value: string) => {
      searchGameKeyword.value = value;
      await fetchGameOptions();
    };
    const onPopupScroll = async (e: Event) => {
      // 前缀树API不支持分页，所以不需要滚动加载更多
      // 保留函数以避免模板报错，但不执行任何操作
    };
    const fetchGameOptions = async (append = false) => {
      searching.value = true;
      try {
        const res = await findGamesByPrefixApi({
          prefix: searchGameKeyword.value || '',
        });
        const records = res?.records || [];
        searchGameTotal.value = records.length;
        const options = records.map((item: any) => ({
          label: item.nameZh || item.gameName || item.name,
          value: item.appId || item.gameId || item.id,
          iconUrl: item.iconUrl || item.icon || '',
        }));
        // 前缀树API返回所有匹配结果，直接替换
        searchGameOptions.value = options;
      } finally {
        searching.value = false;
      }
    };

    // 选中某个游戏
    const onSelectGame = (gameId: string) => {
      searchGameId.value = gameId;
    };

    // 点击搜索按钮，跳转到详情
    const onSearchBtn = () => {
      if (!searchGameId.value) return;
      router.push({
      name: 'applicationDetails',
      query: {
        appId: searchGameId.value,
      },
    });
    };
    // 新增：综合评分数据
    const commentSummary = ref({
      rating: 0,
      reviewCount: 0,
      goodRate: 0,
      starCounts: [0, 0, 0, 0, 0], // 5星到1星
    });

    function go_jie() {
      chear_jie.value = true;
      chear_chan.value = false;
      chear_pin.value = false;
    }

    function go_chan() {
      chear_jie.value = false;
      chear_chan.value = true;
      chear_pin.value = false;
    }

    function go_pin() {
      chear_jie.value = false;
      chear_chan.value = false;
      chear_pin.value = true;
    }

    // 评论数据
    const comments=ref([
      {
        name:"llili",
        comment:"《幻影街编年史》以其独特的艺术风格和深刻的故事线吸引了我。游戏中的每一个角色都有自己的背景故事，让玩家在探索这个幻想城市的同时，也能深入了解每个角色的内心世界。解谜元素与故事情节完美结合，使得每一次冒险都充满了新鲜感。",
        time:"2025/2/4",
        platform:"App Store",
        ip:"北京",
        star:"4"
      },
      {
        name:"llili",
        comment:"这是一款非常适合策略爱好者的游戏。它不仅提供了广阔的宇宙空间供玩家探索，还通过复杂的外交系统和战斗机制增加了游戏的深度。尽管初期上手难度较大，但是一旦掌握了基本规则，《星际征途：奥瑞恩协议》就能提供数小时的沉浸式游戏体验。",
        time:"2025/2/4",
        platform:"App Store",
        ip:"北京",
        star:"4"
      },
      {
        name:"llili",
        comment:"末日工坊》是一款出色的生存建造类游戏，它挑战了玩家在资源稀缺的世界中求生的能力。虽然游戏的图形表现并不是最顶尖的，但是其创新的建造和资源管理系统让人印象深刻。游戏中的天气变化和动态事件为玩家带来了额外的挑战和乐趣。",
        time:"2025/2/4",
        platform:"App Store",
        ip:"北京",
        star:"4"
      }
    ]);

    // 评论加载状态
    const commentLoading = ref(false);

    // 评论分页数据
    const commentPagination = ref({
      current: 1,        // 当前页码
      pageSize: 10,      // 每页显示数量
      total: 0,          // 总数量
      totalPage: 0       // 总页数
    });

    // 分页选项
    const pageSizeOptions = [10, 20, 50, 100];

    // 数字格式化函数
    const formatNumber = (num) => {
      if (!num || num === '0') return '0';
      const number = parseInt(num);
      if (number >= 1000000) {
        return (number / 1000000).toFixed(1) + 'M';
      } else if (number >= 1000) {
        return (number / 1000).toFixed(1) + 'K';
      }
      return number.toLocaleString();
    };

    // 搜索数据
    const Search_content=ref();
    let myChart = ref()
    let option = ref({})

    onMounted(async () => {
  await fetchData();
  await fetchCommentSummary();
    await fetchComments(1); // 加载第1页评论
     await fetchGameOptions();
  init();
})
const fetchData = async () => {
  try {
    const id = route.query.appId || route.query.id || "000000000000100";
    // const response = await defHttp.get({ url: '/appInfo/queryById', params: { appId: id } });
    const response = await queryByIdApi({ appId: id });
    console.log('应用详情：',response)
    gameInfo.value = response;
    // 处理 screenshotUrls 字段
    let urls = response.screenshotUrls;
    if (urls) {
      if (Array.isArray(urls)) {
        screenshotList.value = urls;
      } else if (typeof urls === 'string') {
        try {
          // 先去除首尾引号
          let str = urls.trim();
          if (str.startsWith('"') && str.endsWith('"')) {
            str = str.slice(1, -1);
          }
          // 还原转义
          str = str.replace(/\\"/g, '"');
          // 再 JSON.parse 一次
          const arr = JSON.parse(str);
          screenshotList.value = Array.isArray(arr)
            ? arr.map(u => u.trim())
            : [];
        } catch {
          screenshotList.value = [];
        }
      } else {
        screenshotList.value = [];
      }
    } else {
      screenshotList.value = [];
    }
    // 新增：处理排名历史
    //rankingHistory.value = Array.isArray(response.rankingHistory) ? response.rankingHistory : [];
  } catch (error) {
    console.error('数据获取失败:', error);
    screenshotList.value = [];
    //rankingHistory.value = [];
  }
};


const fetchComments = async (page = 1) => {
  commentLoading.value = true;
  try {
    const appId = route.query.appId || route.query.id || "6444826882";
    // 使用正确的接口获取用户评论列表，添加分页参数
    const res = await getCommentApi({
      appId,
      pageNo: page,
      pageSize: commentPagination.value.pageSize
    });
    console.log('用户评论数据（从getCommentApi）：',res)

    // 检查返回数据中是否包含评论列表
    let commentList: any[] = [];
    if (Array.isArray(res)) {
      commentList = res;
    } else if (res && res.commentList) {
      commentList = res.commentList;
    } else if (res && res.comments) {
      commentList = res.comments;
    } else if (res && res.records) {
      commentList = res.records;
    } else if (res && res.data) {
      commentList = res.data;
    } else if (res && res.result) {
      commentList = res.result;
    }

    console.log('提取的评论列表：', commentList);
    if (commentList.length > 0) {
      console.log('第一条评论数据结构：', commentList[0]);
    }

    // 格式化评论数据，确保前端需要的字段都存在
    const formattedComments = commentList.map((item: any) => ({
      name: item.name || item.userName || item.reviewer || item.user || '匿名用户',
      star: item.star || item.rating || item.score || item.point || 5,
      comment: item.comment || item.content || item.review || item.text || '',
      time: item.time || item.createTime || item.reviewTime || item.date || '',
      platform: item.platform || item.source || item.store || 'App Store',
      ip: item.ip || item.location || item.region || item.area || ''
    }));

    comments.value = formattedComments;
    console.log('格式化后的评论数据：', comments.value);

    // 更新分页信息
    commentPagination.value.current = page;
    commentPagination.value.total = res.totalNums || 0;
    commentPagination.value.totalPage = res.totalPage || 0;

    console.log('分页信息：', commentPagination.value);
  } catch (e) {
    console.error('获取评论失败:', e);
    comments.value = [];
    // 重置分页信息
    commentPagination.value.total = 0;
    commentPagination.value.totalPage = 0;
  } finally {
    commentLoading.value = false;
  }
};

// 分页切换处理函数
const onCommentPageChange = async (page: number) => {
  console.log('切换到第', page, '页');
  await fetchComments(page);
  // 滚动到评论区域顶部
  const commentsContainer = document.querySelector('.comments-list-container');
  if (commentsContainer) {
    commentsContainer.scrollTop = 0;
  }
};

// 每页显示数量变化处理函数
const onPageSizeChange = async () => {
  console.log('每页显示数量变更为:', commentPagination.value.pageSize);
  // 重置到第一页
  commentPagination.value.current = 1;
  await fetchComments(1);
};

// 新增：获取综合评分数据
const fetchCommentSummary = async () => {
  try {
    const appId = route.query.appId || route.query.id || "000000000000100";
    // const res = await defHttp.get({ url: '/shop/getCommentCount', params: { appId:appId}});
    const res = await getCommentCountApi({ appId });
    // 假设返回格式为 { rating: 4.5, reviewCount: 12345, goodRate: 0.98, starCounts: [5000, 2000, 1000, 500, 200] }
// 构造 5星到1星的数量数组
    const starCounts: number[] = [0, 0, 0, 0, 0];
    let totalScore = 0;
    let totalCount = 0;
    res.forEach((item: { point: number; count: number }) => {
      if (item.point >= 1 && item.point <= 5) {
        starCounts[5 - item.point] = item.count;
        totalScore += item.point * item.count;
        totalCount += item.count;
      }
    });
    // 计算平均分
    const rating = totalCount ? (totalScore / totalCount) : 0;
    // 计算好评率（4星和5星占比）
    const goodCount = (starCounts[0] || 0) + (starCounts[1] || 0);
    const goodRate = totalCount ? (goodCount / totalCount) : 0;
    commentSummary.value.rating = Number(rating.toFixed(1));
    commentSummary.value.reviewCount = totalCount;
    commentSummary.value.goodRate = goodRate;
    commentSummary.value.starCounts = starCounts;
  } catch (e) {
  }
};
function getStarPercent(star: number) {
  const total = commentSummary.value.starCounts.reduce((a, b) => a + b, 0) || 1;
  return Math.round((commentSummary.value.starCounts[5 - star] / total) * 100);
}
    // 初始化波浪图
const init = () => {
  // 只展示当前详情的一个点
  const rawDate = gameInfo.value.lastUpdated || '';
const formattedDate = rawDate ? dayjs(new Date(rawDate)).format('YYYYMMDD') : '';
  const xData = [formattedDate || ''];
  const yData = [gameInfo.value.ranking || 0];
  const descriptions = [gameInfo.value.lastDescription || ''];

  myChart.value = echarts.init(document.getElementById('ranking_echar'));
  option.value = {
    title: { text: '' },
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        // 只有一个点
        const idx = params[0].dataIndex;
        return `
          <div>
            <div>日期: ${xData[idx]}</div>
            <div>排名: ${yData[idx]}</div>
            <div>描述: ${descriptions[idx] || ''}</div>
          </div>
        `;
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData,
      name: '日期'
    },
    yAxis: {
      type: 'value',
      name: '排名'
    },
    series: [
      {
        name: '排名',
        type: 'line',
        stack: 'Total',
        data: yData,
        areaStyle: {}, // 波浪填充
        symbol: 'circle',
        smooth: true
      }
    ]
  };
  myChart.value.setOption(option.value);
};
    
    
  </script>

  <style scoped>
  /* 主容器样式 */
  .app-detail-container {
    max-width: 100vw;
    overflow-x: hidden;
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
  }

  /* 头部卡片样式 */
  .app-header-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 12px;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    position: relative;
  }

  .app-header-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px;
    position: relative;
    z-index: 1;
  }

  .app-info-section {
    display: flex;
    align-items: center;
    gap: 24px;
    flex: 1;
  }

  .app-icon-wrapper {
    position: relative;
  }

  .app-icon {
    width: 80px;
    height: 80px;
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    border: 3px solid rgba(255, 255, 255, 0.3);
  }

  .app-details {
    color: white;
  }

  .app-title {
    font-size: 28px;
    font-weight: bold;
    margin: 0 0 8px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .app-developer {
    font-size: 16px;
    margin: 0 0 12px 0;
    opacity: 0.9;
  }

  .app-rating-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    backdrop-filter: blur(10px);
  }

  /* 搜索区域样式 */
  .search-section {
    flex: 0 0 400px;
  }

  .search-wrapper {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .search-select {
    flex: 1;
    min-width: 280px;
  }

  .search-select .ant-select-selector {
    background: rgba(255, 255, 255, 0.15) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 25px !important;
    backdrop-filter: blur(10px);
    color: white !important;
    height: 40px !important;
  }

  .search-select .ant-select-selection-placeholder {
    color: rgba(255, 255, 255, 0.8) !important;
  }

  .search-select .ant-select-arrow {
    color: rgba(255, 255, 255, 0.8) !important;
  }

  .search-option {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .option-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
  }

  .search-btn {
    height: 40px !important;
    border-radius: 25px !important;
    background: linear-gradient(135deg, #29ade6, #1890ff) !important;
    border: none !important;
    box-shadow: 0 4px 12px rgba(41, 173, 230, 0.4) !important;
    font-weight: 500;
    padding: 0 20px !important;
  }

  .search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(41, 173, 230, 0.6) !important;
  }

  /* 应用信息网格样式 */
  .app-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    padding: 20px 30px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    margin: 0 12px;
    border-radius: 12px;
    margin-top: -8px;
  }

  .info-item {
    text-align: center;
    padding: 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .info-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 8px;
    font-weight: 500;
  }

  .info-value {
    font-size: 16px;
    color: white;
    font-weight: bold;
    word-break: break-all;
  }

  .info-value.price {
    color: #52c41a;
  }

  .info-value.app-id {
    font-family: monospace;
    font-size: 14px;
  }

  .support-url {
    color: #29ade6 !important;
    text-decoration: none;
    transition: all 0.3s ease;
  }

  .support-url:hover {
    color: #1890ff !important;
    text-decoration: underline;
  }

  .default-company {
    color: rgba(255, 255, 255, 0.9);
  }

  /* 统计区域样式 */
  .stats-section {
    margin: 20px 12px;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
  }

  .stat-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.8);
  }

  .stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  .stat-icon {
    font-size: 48px;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  }

  .stat-content {
    flex: 1;
  }

  .stat-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
  }

  .stat-subtitle {
    font-size: 14px;
    color: #666;
    margin-bottom: 12px;
  }

  .stat-value {
    font-size: 36px;
    font-weight: bold;
    line-height: 1;
  }

  .download-value {
    color: #52c41a;
  }

  .revenue-value {
    color: #fa8c16;
  }

  .ranking-value {
    color: #722ed1;
  }
  .Information{
    width: 95%;
    margin-left: 3%;
    margin-top: 1%;
    height: 100px;
  }
  .introduction_title{
    width: 230px;
    height: 50px;
    margin-left: 1%;
    
  }
  .bottom_card{
    width: 98.8%;
    margin-left: 12px;
    margin-top: 2%;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.8);
  }

  /* 导航标签样式优化 */
  .introduction_title {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 12px 12px 0 0;
    padding: 20px;
    border-bottom: 1px solid #e8e8e8;
  }

  .introduction_title .ant-col {
    cursor: pointer;
    padding: 10px 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-align: center;
    font-weight: 500;
  }

  .introduction_title .ant-col:hover {
    background-color: rgba(41, 173, 230, 0.1);
    color: #29ade6;
  }

/* 分页样式重构 */
.comments-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 20px 30px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8e8e8;
}

.pagination-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.pagination-center {
  display: flex;
  align-items: center;
  gap: 16px;
}

.pagination-size {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-label {
  color: #666;
  font-size: 14px;
}

.page-size-select {
  padding: 6px 12px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background-color: #fff;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.page-size-select:hover {
  border-color: #018ffb;
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 20px;
  border: 2px solid #29ade6;
  border-radius: 25px;
  background-color: #fff;
  color: #29ade6;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
  justify-content: center;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #29ade6;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(41, 173, 230, 0.3);
}

.pagination-btn:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
  color: #ccc;
  border-color: #e8e8e8;
  transform: none;
  box-shadow: none;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #f0f8ff;
  border-radius: 20px;
  border: 1px solid #29ade6;
}

.current-page {
  font-size: 18px;
  font-weight: bold;
  color: #29ade6;
}

.page-separator {
  font-size: 16px;
  color: #999;
  margin: 0 4px;
}

.total-page {
  font-size: 16px;
  color: #666;
}

.total-count {
  color: #666;
  font-size: 14px;
  padding: 6px 12px;
  background-color: #f8f9fa;
  border-radius: 16px;
  border: 1px solid #e8e8e8;
}

.total-pages {
  font-weight: bold;
  color: #29ade6;
}

/* 评论列表容器样式 */
.comments-list-container {
  min-height: 600px;
  max-height: 600px;
  overflow-y: auto;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin: 20px 0;
  border: 1px solid #e8e8e8;
  scroll-behavior: smooth;
}

/* 自定义滚动条样式 */
.comments-list-container::-webkit-scrollbar {
  width: 8px;
}

.comments-list-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.comments-list-container::-webkit-scrollbar-thumb {
  background: #29ade6;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.comments-list-container::-webkit-scrollbar-thumb:hover {
  background: #1890ff;
}

/* 评论区域样式优化 */
.comment-item {
  border-left: 3px solid #29ade6;
  margin-bottom: 20px;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 0 8px 8px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.comment-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.comment-item:last-child {
  margin-bottom: 0;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.comment-user {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.comment-star {
  font-size: 14px;
}

.comment-meta {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  font-size: 12px;
  color: #666;
}

.comment-platform,
.comment-time,
.comment-location {
  font-weight: lighter;
}

.comment-content {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
  max-width: 100%;
  overflow-wrap: break-word;
}

/* 空状态样式 */
.no-comments {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #999;
}

.no-comments-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.no-comments-text {
  font-size: 16px;
  color: #666;
}

/* 加载状态样式 */
.comments-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  gap: 20px;
}

.loading-text {
  font-size: 16px;
  color: #666;
  margin-top: 16px;
}

/* 评论过渡动画 */
.comment-fade-enter-active,
.comment-fade-leave-active {
  transition: all 0.5s ease;
}

.comment-fade-enter-from {
  opacity: 0;
  transform: translateY(30px);
}

.comment-fade-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

.comment-fade-move {
  transition: transform 0.5s ease;
}

/* 评论头部样式 */
.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.page-indicator {
  background: linear-gradient(135deg, #29ade6, #1890ff);
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(41, 173, 230, 0.3);
}

/* 评分区域样式优化 */
.rating-section-title {
  font-weight: bold;
  font-size: 16px;
  margin-top: 0.3%;
  margin-left: 1%;
  color: #333;
}

.rating-section-divider {
  background-color: #29ade6;
  width: 65px;
  height: 3px;
  margin-left: 15px;
  margin-bottom: 20px;
}

.rating-summary-container {
  display: flex;
  gap: 40px;
  margin: 20px 2%;
  flex-wrap: wrap;
}

.rating-overview {
  flex: 0 0 300px;
  text-align: center;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 8px;
}

.rating-score {
  font-size: 72px;
  color: #29ade6;
  font-weight: bold;
  margin-bottom: 10px;
}

.rating-stars {
  margin-bottom: 15px;
}

.rating-info {
  font-size: 14px;
  color: #666;
}

.good-rate {
  margin-top: 8px;
  font-weight: bold;
  color: #52c41a;
}

.rating-breakdown {
  flex: 1;
  min-width: 300px;
}

.star-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
  padding: 8px;
  background-color: #fafafa;
  border-radius: 4px;
}

.star-label {
  flex: 0 0 120px;
}

.star-progress {
  flex: 1;
}

.star-count {
  flex: 0 0 60px;
  text-align: right;
  font-weight: bold;
  color: #666;
}

/* 评论区域容器样式 */
.comments {
  max-width: 100%;
  overflow-x: hidden;
  padding: 0 1%;
  box-sizing: border-box;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .app-info-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    padding: 20px;
  }

  .app-info-section {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .search-section {
    flex: none;
    width: 100%;
  }

  .search-wrapper {
    flex-direction: column;
    gap: 12px;
  }

  .search-select {
    min-width: auto;
  }

  .app-info-grid {
    grid-template-columns: 1fr;
    padding: 15px;
    margin: 0 8px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .stat-card {
    padding: 20px;
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    font-size: 32px;
  }

  .stat-value {
    font-size: 28px;
  }

  .rating-summary-container {
    flex-direction: column;
    gap: 20px;
  }

  .rating-overview {
    flex: none;
  }

  .star-row {
    flex-wrap: wrap;
    gap: 8px;
  }

  .star-label {
    flex: 0 0 100px;
  }

  .comment-meta {
    flex-wrap: wrap;
    gap: 8px;
  }
}

/* 图表容器样式 */
.chart-container {
  width: 100%;
  max-width: 1200px;
  height: 500px;
  margin: 4% auto 0;
  padding: 0 2%;
  box-sizing: border-box;
}
</style>