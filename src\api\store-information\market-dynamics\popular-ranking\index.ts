import { defHttp } from '/@/utils/http/axios';

enum Api {
    getIncomeAndDownloadByType = '/shop/getIncomeAndDownloadByType',
    shopBehavior = '/shop/shopBehavior',
    getAllGenre = '/shop/getAllGenre',
    getAllDevice = '/shop/getAllDevice',
    // queryByParam = '/downloadTimesAndRevenue/queryByParam',
    top = '/shop/topStore/top',
    currentMarket = '/shop/topStore/currentMarket',


}

// 获取用户数据部分income,genre,downloadTimes
export function getIncomeAndDownloadByTypeApi(params){
    return defHttp.get({
        url: Api.getIncomeAndDownloadByType,
        params
    })
}

// 获取三个榜单数据
export function topApi(data){
    return defHttp.post({
        url:Api.top,
        data
    })
}

// 获取输入框游戏类型
export function getAllGenreApi(){
    return defHttp.get({
        url: Api.getAllGenre,
    })
}

// 获取输入框游戏平台类型
export function getAllDeviceApi(){
    return defHttp.get({
        url: Api.getAllDevice,
    })
}

// 获得市场现状数据
export function currentMarketApi(data){
    return defHttp.post({
        url: Api.currentMarket,
        data
    })
}



