<template>
  <div>
    <div class="up_card">
      <a-row style="margin-left: 1%; padding-top: 1%">
        <a-col :span="2">
          <!-- <img class="toux" :src="topAppInfo.logo || './component/toux.png'" alt=""> -->
        </a-col>
        <a-col :span="8">
          <a-row style="font-size: 20px; font-weight: bold; margin-top: 3%">{{ topAppInfo.publisher || '上海米哈游' }}</a-row>
          <a-row style="font-size: 14px; font-weight: lighter; margin-top: 2%">ID: {{ topAppInfo.id || '未知ID' }}</a-row>
        </a-col>
        <a-col
          :span="8"
          :offset="6"
        >
          <a-select
            v-model:value="Search_content"
            show-search
            placeholder="搜索发行商"
            style="width: 200px"
            :default-active-first-option="false"
            :show-arrow="true"
            :filter-option="false"
            
            :not-found-content="publisherLoading ? '加载中...' : '未找到'"
            @search="onPublisherSearch"
            @select="onPublisherSelect"
            @popup-scroll="onPopupScroll"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
            <a-select-option
              v-for="option in publisherOptions"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-col>
      </a-row>

      <div style="margin-top: 3%; margin-left: 4%">
        <a-row style="margin-top: 2%">
          <a-col :span="8">
            <a-row>
              <a-col
                :span="24"
                style="font-weight: bold; font-size: 16px"
              >发行游戏数</a-col>
            </a-row>
            <a-row>
              <a-col
                :span="24"
                style="font-size: 16px"
              >{{ topAppInfo.gameNumber ?? '--' }}</a-col>
            </a-row>
          </a-col>

          <a-col :span="8">
            <a-row>
              <a-col
                :span="24"
                style="font-weight: bold; font-size: 16px"
              >净收入</a-col>
            </a-row>
            <a-row>
              <a-col
                :span="24"
                style="font-size: 16px"
              >{{ topAppInfo.incomeSum ?? '--' }}</a-col>
            </a-row>
          </a-col>

          <a-col :span="8">
            <a-row>
              <a-col
                :span="24"
                style="font-weight: bold; font-size: 16px"
              >热门国家/地区</a-col>
            </a-row>
            <a-row>
              <a-col
                :span="24"
                style="font-size: 16px"
              >{{ topAppInfo.region || '--' }}</a-col>
            </a-row>
          </a-col>
        </a-row>
        <a-row style="margin-top: 2%">
          <a-col :span="8">
            <a-row>
              <a-col
                :span="24"
                style="font-weight: bold; font-size: 16px"
              >总下载量</a-col>
            </a-row>
            <a-row>
              <a-col
                :span="24"
                style="font-size: 16px"
              >{{ topAppInfo.downloadTimeSum ?? '--' }}</a-col>
            </a-row>
          </a-col>
          <a-col :span="8">
            <a-row>
              <a-col
                :span="24"
                style="font-weight: bold; font-size: 16px"
              >开发者网站</a-col>
            </a-row>
            <a-row>
              <a-col
                :span="24"
                style="font-size: 16px"
              >{{ topAppInfo.site || '暂无' }}</a-col>
            </a-row>
          </a-col>
          <a-col :span="8" />
        </a-row>
      </div>
    </div>

    <!-- 筛选区 -->
    <div class="filter-bar">
      <a-space
        :size="16"
        align="center"
      >
        <a-range-picker
          v-model:value="value1"
          :presets="rangePresets"
          @change="onRangeChange"
          style="width: 260px"
        />
        <a-select
          v-model:value="country_data"
          mode="multiple"
          allowClear
          size="middle"
          placeholder="请选择国家/地区"
          style="width: 200px"
          :max-tag-count="1"
          :max-tag-placeholder="maxTagPlaceholder"
          @change="handleCountryChange"
        >
          <a-select-option
            value="all"
            @click="selectAllCountries"
          >选择全部</a-select-option>
          <a-select-option
            v-for="country in select_country"
            :key="country.value"
            :value="country.value"
          >
            {{ country.label }}
          </a-select-option>
        </a-select>
        <a-select
          v-model:value="equipment"
          mode="multiple"
          allowClear
          placeholder="请选择平台"
          style="width: 200px"
          :max-tag-count="1"
          :max-tag-placeholder="maxTagPlaceholder"
          @change="handleEquipmentChange"
        >
          <a-select-option
            value="all"
            @click="selectAllEquipment"
          >选择全部</a-select-option>
          <a-select-option
            v-for="item in equipment_data"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
        <a-select
          v-model:value="selectedGameCategory"
          mode="multiple"
          allowClear
          placeholder="请选择游戏类别"
          style="width: 200px"
          :max-tag-count="1"
          :max-tag-placeholder="maxTagPlaceholder"
          @change="handleGameCategoryChange"
        >
          <a-select-option
            value="all"
            @click="selectAllGames"
          >选择全部</a-select-option>
          <a-select-option
            v-for="game in games"
            :key="game.value"
            :value="game.value"
          >
            {{ game.label }}
          </a-select-option>
        </a-select>
        <a-select
          v-model:value="selectedPayment"
          mode="multiple"
          allowClear
          placeholder="请选择付费情况"
          style="width: 200px"
          :max-tag-count="1"
          :max-tag-placeholder="maxTagPlaceholder"
          @change="handlePaymentChange"
        >
          <a-select-option
            value="all"
            @click="selectAllPayment"
          >选择全部</a-select-option>
          <a-select-option
            v-for="item in paymentOptions"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
        <a-button
          type="primary"
          @click="bijiao"
        >确定</a-button>
      </a-space>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <a-table
        :columns="columns"
        :data-source="Publisher_Date"
        :pagination="paginationProp"
        row-key="publisher"
        size="middle"
        bordered
        class="custom-table"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'ranking'">
            {{ record.ranking === null || record.ranking === undefined || record.ranking === '' ? '-' : record.ranking }}
          </template>
          <template v-if="column.key === 'publisher'">
            <span
              style="color: #1890ff; cursor: pointer"
              @click.stop="goToPublisherDetail(record)"
            >
              {{ record.publisher }}
            </span>
          </template>
          <template v-else-if="column.key === 'percentage_download'">
            <div style="display: flex; align-items: center; gap: 8px">
              <a-progress
                :percent="Number((record.downloadTimePer * 100).toFixed(2))"
                size="small"
                :show-info="false"
                stroke-color="#40a9ff"
                style="flex: 1; min-width: 0"
              />
              <span style="min-width: 48px; text-align: right; font-weight: bold; color: #40a9ff">
                {{ Number((record.downloadTimePer * 100).toFixed(2)) }}%
              </span>
            </div>
          </template>
          <template v-else-if="column.key === 'percentage_income'">
            <div style="display: flex; align-items: center; gap: 8px">
              <a-progress
                :percent="Number((record.incomeSumPer * 100).toFixed(2))"
                size="small"
                :show-info="false"
                stroke-color="#ff9c6e"
                style="flex: 1; min-width: 0"
              />
              <span style="min-width: 48px; text-align: right; font-weight: bold; color: #ff9c6e">
                {{ Number((record.incomeSumPer * 100).toFixed(2)) }}%
              </span>
            </div>
          </template>
          <template v-else>
            {{ record[column.dataIndex] }}
          </template>
        </template>
        <!-- 空数据显示 -->
        <template #emptyText>
          <div class="empty-data-container">
            <i class="empty-icon">🔍</i>
            <p>没有找到匹配的数据</p>
            <p class="empty-data-tip">请尝试调整筛选条件后再次查询</p>
          </div>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script lang="ts" name="basic-table-demo" setup>
import { ref, onMounted, h } from 'vue';
import { useRouter } from 'vue-router';
import { getPublisherInfoApi, getAllGenreApi, getAllDeviceApi, queryPublisherByNameApi } from '@/api/store-information/market-dynamics/publisher-ranking/index';
import type { CascaderProps, SelectProps } from 'ant-design-vue';
import { Cascader } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import { columns } from './component/From_component.data';
import { SearchOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import * as echarts from 'echarts';

const Publisher_Date = ref<any[]>([]);
const topAppInfo = ref<any>({});
const total = ref(0);
const page = ref(1);
const pageSize = ref(10);

// 定义 RangeValue 类型
type RangeValue = [Dayjs, Dayjs];

// 设备选择的数据
const equipment_data = ref([
  {
    label: 'App Store',
    value: 'apple',
  },
  // {
  //   label: 'Google Play',
  //   value: 'google',
  // },
]);
// 选择后的设备数据
const equipment = ref<string[]>([]);
const games = ref<{ value: string; label: string }[]>([]);
const fetchGameGenres = async () => {
  try {
    const res = await getAllGenreApi();
    games.value = [
      ...(res || []).map((item: any) => ({
        value: item.value,
        label: item.value,
      })),
    ];
  } catch (e) {}
};
const options: CascaderProps['options'] = equipment_data.value;

// 时间选择
const rangePresets: { label: string; value: [Dayjs, Dayjs] }[] = [
  { label: '最近一周', value: [dayjs().add(-7, 'day'), dayjs()] },
  { label: '最近一个月', value: [dayjs().add(-1, 'month'), dayjs()] },
  { label: '最近三个月', value: [dayjs().add(-3, 'month'), dayjs()] },
  { label: '最近半年', value: [dayjs().add(-6, 'month'), dayjs()] },
  { label: '最近一年', value: [dayjs().add(-1, 'year'), dayjs()] },
  { label: '最近三年', value: [dayjs().add(-3, 'year'), dayjs()] },
];

// 设置默认值为近一年
const value1 = ref<RangeValue>([dayjs().add(-1, 'year'), dayjs()]);

// 国家地区的选择
const select_country = ref([
  { value: '菲律宾', label: '菲律宾' },
  { value: '柬埔寨', label: '柬埔寨' },
  { value: '马来西亚', label: '马来西亚' },
  { value: '泰国', label: '泰国' },
  { value: '文莱', label: '文莱' },
  { value: '新加坡', label: '新加坡' },
  { value: '印度尼西亚', label: '印度尼西亚' },
  { value: '越南', label: '越南' },
  { value: '缅甸', label: '缅甸' },
  { value: '中国台湾', label: '中国台湾' },
  { value: '老挝人民民主共和国', label: '老挝人民民主共和国' },
]);

const country_1 = ref<SelectProps['options']>(select_country.value);
const country_data = ref<string[]>([]);
// 搜索数据
const Search_content = ref('');

// 付费情况选择
const paymentOptions = ref([
  { value: 'free', label: '免费' },
  { value: 'paid', label: '付费' },
]);
const selectedPayment = ref<string[]>([]);

// 请求数据
const fetchAppList = async () => {
  try {
    let startTime = '';
    let endTime = '';
    if (value1.value && value1.value.length === 2) {
      startTime = value1.value[0].format('YYYY-MM-DD');
      endTime = value1.value[1].format('YYYY-MM-DD');
    }
    const params = {
      pageNo: page.value,
      pageSize: pageSize.value,
      platformName: equipment.value.length > 0 ? equipment.value.map((item) => (item === 'apple' ? 'App Store' : item)) : undefined,
      countryName: country_data.value.length > 0 ? country_data.value : undefined,
      gameCategory: selectedGameCategory.value.length > 0 ? selectedGameCategory.value : undefined,
      paymentType: selectedPayment.value.length > 0 ? selectedPayment.value : undefined,
      startTime,
      endTime,
    };
    const res = await getPublisherInfoApi(params);

    // 处理返回值，添加"选择全部"选项并修改平台名称
    const records = res.records || [];
    Publisher_Date.value = records.map((item: any) => ({
      ...item,
      platform: item.platform === 'apple' ? 'App Store' : item.platform,
    }));

    total.value = res.total || 0;
    if (Publisher_Date.value.length > 0) {
      topAppInfo.value = Publisher_Date.value[0];
    }
  } catch (e) {
    Publisher_Date.value = [];
    topAppInfo.value = {};
  }
};

const router = useRouter();

const goToPublisherDetail = (record) => {
  router.push({
    name: 'publisherDetails',
    query: {
      id: record.publisher,
    },
  });
};

function bijiao() {
  fetchAppList();
}

// 查询方法
const handleSearch = () => {
  page.value = 1;
  fetchAppList();
};
const selectedGameCategory = ref<string[]>([]);

onMounted(() => {
  fetchAppList();
  fetchGameGenres();
  fetchAllDevice();
  loadPublisherOptions();
});

const paginationProp = ref({
  showSizeChanger: false,
  showQuickJumper: true,
  pageSize,
  current: page,
  total,
  showTotal: (total) => `总 ${total} 条`,
  onChange: pageChange,
  onShowSizeChange: pageSizeChange,
});

function pageChange(p, pz) {
  page.value = p;
  pageSize.value = pz;
  fetchAppList();
}
function pageSizeChange(current, size) {
  pageSize.value = size;
  fetchAppList();
}
const onRangeChange = (dates: RangeValue, dateStrings: string[]) => {
  if (dates) {
    console.log('From: ', dates[0], ', to: ', dates[1]);
    console.log('From: ', dateStrings[0], ', to: ', dateStrings[1]);
  } else {
    console.log('Clear');
  }
};

const fetchAllDevice = async () => {
  try {
    const res = await getAllDeviceApi();
    // 处理返回值，将apple转换为App Store，并添加"选择全部"选项
    const deviceOptions = (res || []).map((item: any) => ({
      value: item.value,
      label: item.value === 'apple' ? 'App Store' : item.value,
    }));

    // 添加"选择全部"选项
    equipment_data.value = [...deviceOptions];
  } catch (e) {
    equipment_data.value = [];
  }
};

const publisherSearchValue = ref('');
const publisherOptions = ref<{ label: string; value: string }[]>([]);
const publisherLoading = ref(false);
const publisherPage = ref(1);
const publisherPageSize = ref(10);
const publisherTotal = ref(0);

// 发行商搜索
const onPublisherSearch = async (value: string) => {
  publisherSearchValue.value = value;
  publisherPage.value = 1;
  await loadPublisherOptions();
};

// 加载发行商选项
const loadPublisherOptions = async () => {
  try {
    const response = await queryPublisherByNameApi({
      publisherId: publisherSearchValue.value,
      page: publisherPage.value,
      pageSize: publisherPageSize.value
    });

    if (response && response.records) {
      publisherOptions.value = response.records.map((item) => ({
        value: item.publisher,
        label: item.name,
      }));
    }
  } catch (error) {
    console.error('加载发行商选项失败:', error);
  }
};

// 下拉框滚动到底部时加载更多
const onPopupScroll = (e: Event) => {
  const target = e.target as HTMLElement;
  if (target.scrollTop + target.offsetHeight >= target.scrollHeight - 20) {
    if (publisherOptions.value.length < publisherTotal.value) {
      publisherPage.value++;
      loadPublisherOptions();
    }
  }
};

// 发行商选择
const onPublisherSelect = (value: string) => {
  if (value) {
    router.push({
      name: 'publisherDetails',
      query: {
        id: value,
      },
    });
  }
};

// 标签溢出处理函数
const maxTagPlaceholder = (omittedValues: any[]) => {
  return h('span', { class: 'ellipsis-tag' }, '...');
};
// 国家选择相关函数
const selectAllCountries = () => {
  country_data.value = select_country.value.map((item) => item.value);
};
const handleCountryChange = (value: string[]) => {
  if (value.includes('all')) {
    country_data.value = select_country.value.map((item) => item.value);
  }
};
// 平台选择相关函数
const selectAllEquipment = () => {
  equipment.value = equipment_data.value.map((item) => item.value);
};
const handleEquipmentChange = (value: string[]) => {
  if (value.includes('all')) {
    equipment.value = equipment_data.value.map((item) => item.value);
  }
};
// 游戏类别选择相关函数
const selectAllGames = () => {
  selectedGameCategory.value = games.value.map((item) => item.value);
};
const handleGameCategoryChange = (value: string[]) => {
  if (value.includes('all')) {
    selectedGameCategory.value = games.value.map((item) => item.value);
  }
};
// 付费情况选择相关函数
const selectAllPayment = () => {
  selectedPayment.value = paymentOptions.value.map((item) => item.value);
};
const handlePaymentChange = (value: string[]) => {
  if (value.includes('all')) {
    selectedPayment.value = paymentOptions.value.map((item) => item.value);
  }
};
</script>

<style scoped>
.up_card {
  margin: 24px 2% 0 10px;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 24px;
}

.filter-bar {
  margin: 24px 2% 0 10px;
  display: flex;
  align-items: center;
}

.table-container {
  margin: 24px 2% 0 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 24px;
}

.custom-table {
  width: 100%;
  font-size: 14px;
}

/* 表头样式 */
:deep(.custom-table .ant-table-thead > tr > th) {
  background-color: #c2e8f8;
  color: #333;
  font-weight: bold;
  text-align: center;
  padding: 12px 16px;
}

/* 表格单元格样式 */
:deep(.custom-table .ant-table-tbody > tr > td) {
  padding: 12px 16px;
  text-align: center;
  color: #666;
  vertical-align: middle;
  border-bottom: 1px solid #eee;
}

/* 斑马纹样式 */
:deep(.custom-table .ant-table-tbody > tr:nth-child(odd)) {
  background-color: #ffffff;
}
:deep(.custom-table .ant-table-tbody > tr:nth-child(even)) {
  background-color: #dcf2fb;
}

/* 悬停样式 */
:deep(.custom-table .ant-table-tbody > tr:hover > td) {
  background-color: #e6f7ff;
}

/* 标题链接样式 */
.title-link {
  color: #018ffb;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.title-link:hover {
  background-color: rgba(1, 143, 251, 0.1);
  color: #0170c9;
}

.empty-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.empty-icon {
  font-size: 32px;
  color: #ccc;
  margin-bottom: 10px;
}

.empty-data-container p {
  margin: 0;
  font-size: 16px;
  color: #666;
}

.empty-data-tip {
  font-size: 14px;
  color: #999;
}

.toux {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e6e6e6;
  margin-top: 8px;
  margin-left: 8px;
}

/* 搜索框样式 */
.search_input {
  width: 60%;
  margin-top: 4%;
}

/* 进度条容器样式 */
.progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 进度条样式 */
:deep(.ant-progress) {
  flex: 1;
  min-width: 0;
}

/* 进度条数值样式 */
.progress-value {
  min-width: 48px;
  text-align: right;
  font-weight: bold;
}

/* 下载进度条颜色 */
:deep(.download-progress) {
  stroke-color: #40a9ff;
}

/* 收入进度条颜色 */
:deep(.income-progress) {
  stroke-color: #ff9c6e;
}

/* 链接样式 */
a {
  color: #29ade6;
  word-break: break-all;
  text-decoration: none;
}

a:hover {
  color: #1890ff;
}

/* 表格内容样式 */
:deep(.ant-table-cell) {
  font-size: 14px;
  line-height: 1.5;
}

/* 分页器样式 */
:deep(.ant-pagination) {
  margin-top: 16px;
  text-align: right;
}

/* 选择器样式 */
:deep(.ant-select) {
  width: 200px;
}

/* 日期选择器样式 */
:deep(.ant-picker) {
  width: 260px;
}

/* 按钮样式 */
:deep(.ant-btn-primary) {
  background-color: #1890ff;
  border-color: #1890ff;
}

:deep(.ant-btn-primary:hover) {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* 级联选择器样式 */
:deep(.ant-cascader) {
  width: 200px;
}
</style>
